
import { ArrowLeft } from "lucide-react";
import { useNavigate } from "react-router-dom";

const BookingHeader = () => {
  const navigate = useNavigate();
  
  return (
    <header className="fixed top-0 left-0 right-0 bg-white z-50 px-4 py-3 flex items-center border-b">
      <button onClick={() => navigate(-1)} className="p-2">
        <ArrowLeft className="h-5 w-5 text-gray-700" />
      </button>
      <h1 className="ml-2 text-lg font-semibold">Conferma Prenotazione</h1>
    </header>
  );
};

export default BookingHeader;
