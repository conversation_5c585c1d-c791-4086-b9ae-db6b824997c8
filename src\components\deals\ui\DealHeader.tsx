
import { <PERSON><PERSON><PERSON><PERSON>, Bell } from "lucide-react";
import { useNavigate } from "react-router-dom";

interface DealHeaderProps {
  title?: string;
}
 const DealHeader = ({ title = "Crea Offerta" }: DealHeaderProps) => {
  const navigate = useNavigate();
  
  return (
    <header className="fixed top-0 left-0 right-0 bg-white border-b border-gray-100 max-w-md mx-auto z-50">
      <div className="flex items-center px-4 h-16">
        <button 
          onClick={() => navigate(-1)}
          className="p-2 hover:bg-gray-100 rounded-full"
        >
          <ArrowLeft className="h-5 w-5 text-gray-700" />
        </button>
        <h1 className="ml-3 text-xl font-semibold text-gray-800">{title}</h1>
      </div>
    </header>
  );
};


export default DealHeader;