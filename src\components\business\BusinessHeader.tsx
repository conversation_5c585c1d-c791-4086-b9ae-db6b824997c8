
import { Bell } from "lucide-react";

interface BusinessHeaderProps {
  businessName: string;
}

export const BusinessHeader = ({ businessName }: BusinessHeaderProps) => {
  return (
    <header className="fixed top-0 left-0 right-0 bg-white shadow-sm z-50 px-4 py-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 rounded-full bg-brand-primary/10 flex items-center justify-center text-brand-primary">
            {businessName[0].toUpperCase()}
          </div>
          <span className="font-semibold text-gray-800">{businessName}</span>
        </div>
        <button className="relative">
          <Bell className="h-6 w-6 text-gray-700" />
          <span className="absolute -top-1 -right-1 bg-brand-primary text-white text-xs w-4 h-4 rounded-full flex items-center justify-center">
            3
          </span>
        </button>
      </div>
    </header>
  );
};
