import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { AgentConfiguration } from '@/types/agents';

export const useAgentConfigurations = () => {
  const { data: configurations, isLoading, error } = useQuery({
    queryKey: ['agent-configurations'],
    queryFn: async (): Promise<AgentConfiguration[]> => {
      const { data, error } = await supabase
        .from('agent_configurations')
        .select('*')
        .eq('is_active', true)
        .order('agent_type');

      if (error) {
        console.error('Error fetching agent configurations:', error);
        throw error;
      }

      return data.map(config => ({
        ...config,
        tools: Array.isArray(config.tools) ? config.tools : JSON.parse(String(config.tools || '[]')),
        config: typeof config.config === 'object' ? config.config : JSON.parse(String(config.config || '{}'))
      }));
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  return {
    configurations,
    isLoading,
    error
  };
};