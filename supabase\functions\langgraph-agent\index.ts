import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// ============================================================================
// TYPE DEFINITIONS - Production-ready agent system interfaces
// ============================================================================

interface AgentMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

interface AgentState {
  messages: AgentMessage[];
  current_agent: string;
  context: Record<string, any>;
  user_preferences?: Record<string, any>;
  user_id?: string;
  conversation_history?: AgentMessage[];
}

interface ToolCall {
  name: string;
  arguments: Record<string, any>;
}

interface ToolResult {
  name: string;
  result: any;
  error?: string;
}

interface OpenAIResponse {
  choices: Array<{
    message: {
      content?: string;
      tool_calls?: Array<{
        id: string;
        type: 'function';
        function: {
          name: string;
          arguments: string;
        };
      }>;
    };
  }>;
}

// ============================================================================
// ENVIRONMENT SETUP - Secure configuration management
// ============================================================================

const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const openaiApiKey = Deno.env.get('OPENAI_API_KEY');

if (!openaiApiKey) {
  console.warn('OpenAI API key not found. Agent responses will be limited.');
}

const supabase = createClient(supabaseUrl, supabaseKey);

// ============================================================================
// AGENT CONFIGURATIONS - Production agent definitions with tools and capabilities
// ============================================================================

const AGENT_CONFIGS = {
  router: {
    name: 'Router Agent',
    systemPrompt: `You are CatchUp's intelligent routing agent. Analyze user messages and determine the best specialized agent.

Available agents and their capabilities:
- **booking**: Reservations, cancellations, booking management, availability checks
- **discovery**: Deal recommendations, business search, personalized suggestions
- **support**: General help, account issues, technical problems, platform guidance  
- **business_intelligence**: Analytics, reports, business metrics, data insights

Instructions:
1. Analyze the user's intent and content
2. Route to the most appropriate agent
3. If intent is unclear, ask clarifying questions
4. Always use the route_to_agent tool with reasoning

Respond in Italian unless user specifically requests another language.`,
    tools: ['route_to_agent'],
    model: 'gpt-4o-mini'
  },
  
  booking: {
    name: 'Booking Specialist',
    systemPrompt: `You are CatchUp's booking specialist. Help users with all booking-related activities.

Core responsibilities:
- Search and recommend available deals
- Create new bookings with proper validation
- Manage existing reservations (view, modify, cancel)
- Check real-time availability
- Handle booking-related support issues
- Ensure proper booking flow and confirmation

Tools available:
- search_deals: Find relevant deals based on criteria
- check_availability: Verify time slots and capacity
- create_booking: Process new reservations
- get_booking_details: Retrieve booking information
- cancel_booking: Handle cancellation requests
- get_user_preferences: Access user preferences for better recommendations

Always confirm booking details before finalizing. Handle errors gracefully and provide clear next steps.
Respond in Italian unless user specifically requests another language.`,
    tools: ['search_deals', 'check_availability', 'create_booking', 'cancel_booking', 'get_booking_details', 'get_user_preferences'],
    model: 'gpt-4o-mini'
  },
  
  discovery: {
    name: 'Discovery Specialist',
    systemPrompt: `You are CatchUp's discovery and recommendation engine. Help users find the perfect deals and businesses.

Core responsibilities:
- Provide personalized deal recommendations
- Search businesses by location, category, and criteria
- Surface trending and popular offers
- Filter and sort results based on user preferences
- Explain deal details and value propositions
- Guide users through the discovery process

Tools available:
- search_deals: Advanced deal search with filtering
- search_businesses: Find businesses by criteria
- get_recommendations: AI-powered personalized suggestions
- get_trending_deals: Popular and trending offers
- get_user_preferences: Access user preferences and history
- get_nearby_deals: Location-based recommendations

Focus on understanding user intent and preferences to provide highly relevant suggestions.
Respond in Italian unless user specifically requests another language.`,
    tools: ['search_deals', 'search_businesses', 'get_recommendations', 'get_trending_deals', 'get_user_preferences', 'get_nearby_deals'],
    model: 'gpt-4o-mini'
  },
  
  support: {
    name: 'Customer Support',
    systemPrompt: `You are CatchUp's customer support specialist. Provide friendly, helpful assistance with platform-related issues.

Core responsibilities:
- Answer general platform questions
- Help with account settings and profile management
- Troubleshoot technical issues
- Guide users through platform features
- Escalate complex issues when necessary
- Send notifications and updates

Tools available:
- get_user_profile: Access user account information
- update_user_settings: Modify user preferences and settings
- send_notification: Send important messages to users
- escalate_issue: Create support tickets for complex problems
- get_help_articles: Search knowledge base for solutions

Always be empathetic and solution-focused. Provide step-by-step guidance when needed.
Respond in Italian unless user specifically requests another language.`,
    tools: ['get_user_profile', 'update_user_settings', 'send_notification', 'escalate_issue', 'get_help_articles'],
    model: 'gpt-4o-mini'
  },
  
  business_intelligence: {
    name: 'Business Intelligence',
    systemPrompt: `You are CatchUp's business intelligence specialist. Provide data-driven insights and analytics.

Core responsibilities:
- Generate business performance reports
- Analyze user behavior and trends
- Provide deal effectiveness metrics
- Create custom analytics dashboards
- Interpret data and provide actionable insights
- Track KPIs and business health metrics

Tools available:
- get_business_metrics: Access comprehensive business metrics
- generate_report: Create detailed analytics reports
- get_analytics_data: Query specific data points
- analyze_trends: Identify patterns and trends
- get_user_behavior: Understand user interaction patterns

Present data clearly with visual descriptions and actionable recommendations.
Respond in Italian unless user specifically requests another language.`,
    tools: ['get_business_metrics', 'generate_report', 'get_analytics_data', 'analyze_trends', 'get_user_behavior'],
    model: 'gpt-4o-mini'
  }
};

// ============================================================================
// PRODUCTION TOOL IMPLEMENTATIONS - Real business logic with database integration
// ============================================================================

const TOOLS = {
  // ========== ROUTING TOOLS ==========
  route_to_agent: (args: any) => {
    console.log('Routing to agent:', args);
    return {
      agent: args.agent,
      reasoning: args.reasoning,
      confidence: args.confidence || 0.9
    };
  },

  // ========== DEAL & BOOKING TOOLS ==========
  search_deals: async (args: any) => {
    console.log('Searching deals with parameters:', args);
    
    try {
      let query = supabase
        .from('deals')
        .select(`
          id, title, description, original_price, discounted_price, discount_percentage,
          start_date, end_date, status, images, time_slots,
          businesses!inner (
            id, name, address, city, state, latitude, longitude,
            categories (name)
          )
        `)
        .eq('status', 'published')
        .gte('end_date', new Date().toISOString());

      // Apply filters based on search parameters
      if (args.category) {
        query = query.eq('businesses.categories.name', args.category);
      }
      
      if (args.city) {
        query = query.ilike('businesses.city', `%${args.city}%`);
      }
      
      if (args.max_price) {
        query = query.lte('discounted_price', args.max_price);
      }
      
      if (args.query) {
        query = query.or(`title.ilike.%${args.query}%,description.ilike.%${args.query}%`);
      }

      const { data, error } = await query
        .order('created_at', { ascending: false })
        .limit(args.limit || 10);

      if (error) {
        console.error('Database error in search_deals:', error);
        return { error: error.message, deals: [] };
      }

      // Calculate distance if location provided
      const enhancedDeals = data?.map(deal => ({
        ...deal,
        relevance_score: calculateDealRelevance(deal, args),
        formatted_price: `€${deal.discounted_price} (era €${deal.original_price})`,
        savings: deal.original_price - deal.discounted_price
      })) || [];

      return { 
        deals: enhancedDeals.sort((a, b) => b.relevance_score - a.relevance_score),
        total_found: enhancedDeals.length,
        search_params: args
      };
    } catch (error) {
      console.error('Error in search_deals:', error);
      return { error: 'Failed to search deals', deals: [] };
    }
  },

  check_availability: async (args: any) => {
    console.log('Checking availability for:', args);
    
    try {
      const { data: deal, error } = await supabase
        .from('deals')
        .select('time_slots, start_date, end_date')
        .eq('id', args.deal_id)
        .single();

      if (error || !deal) {
        return { available: false, error: 'Deal not found' };
      }

      // Parse time slots and check for requested date/time
      const timeSlots = deal.time_slots as any;
      const requestedDate = new Date(args.date);
      const dayOfWeek = requestedDate.getDay();

      // Find available slots for the requested day
      const daySchedule = timeSlots?.schedule?.find((s: any) => s.day === dayOfWeek);
      
      if (!daySchedule) {
        return { available: false, reason: 'No slots available for this day' };
      }

      const availableSlots = daySchedule.time_slots?.filter((slot: any) => 
        slot.available_seats > 0 && 
        (!args.time || slot.start_time <= args.time && slot.end_time >= args.time)
      ) || [];

      return {
        available: availableSlots.length > 0,
        slots: availableSlots,
        date: args.date,
        deal_id: args.deal_id
      };
    } catch (error) {
      console.error('Error checking availability:', error);
      return { available: false, error: 'Failed to check availability' };
    }
  },

  create_booking: async (args: any) => {
    console.log('Creating booking:', args);
    
    try {
      // Validate required fields
      if (!args.deal_id || !args.user_id || !args.booking_date || !args.booking_time) {
        return { error: 'Missing required booking information' };
      }

      // Get deal information
      const { data: deal, error: dealError } = await supabase
        .from('deals')
        .select(`
          id, original_price, discounted_price, discount_percentage,
          businesses (name, address)
        `)
        .eq('id', args.deal_id)
        .single();

      if (dealError || !deal) {
        return { error: 'Deal not found or unavailable' };
      }

      // Create booking record
      const bookingData = {
        user_id: args.user_id,
        deal_id: args.deal_id,
        booking_date: args.booking_date,
        booking_time: args.booking_time,
        booking_end_time: args.booking_end_time || '23:59:59',
        original_price: deal.original_price,
        discounted_price: deal.discounted_price,
        discount_percentage: deal.discount_percentage,
        status: 'confirmed',
        qr_data: {
          booking_id: crypto.randomUUID(),
          deal_id: args.deal_id,
          user_id: args.user_id,
          verification_code: Math.random().toString(36).substr(2, 8).toUpperCase()
        }
      };

      const { data: booking, error: bookingError } = await supabase
        .from('bookings')
        .insert(bookingData)
        .select()
        .single();

      if (bookingError) {
        console.error('Error creating booking:', bookingError);
        return { error: 'Failed to create booking' };
      }

      return {
        success: true,
        booking_id: booking.id,
        booking_details: {
          ...booking,
          business_name: deal.businesses?.name,
          business_address: deal.businesses?.address
        },
        qr_code: booking.qr_data,
        savings: deal.original_price - deal.discounted_price
      };
    } catch (error) {
      console.error('Error in create_booking:', error);
      return { error: 'Failed to process booking' };
    }
  },

  cancel_booking: async (args: any) => {
    console.log('Cancelling booking:', args);
    
    try {
      const { data: booking, error: fetchError } = await supabase
        .from('bookings')
        .select('*, deals(title)')
        .eq('id', args.booking_id)
        .eq('user_id', args.user_id)
        .single();

      if (fetchError || !booking) {
        return { error: 'Booking not found or not authorized' };
      }

      if (booking.status === 'cancelled') {
        return { error: 'Booking is already cancelled' };
      }

      // Update booking status
      const { error: updateError } = await supabase
        .from('bookings')
        .update({ 
          status: 'cancelled',
          cancellation_note: args.reason || 'Cancelled by user',
          updated_at: new Date().toISOString()
        })
        .eq('id', args.booking_id);

      if (updateError) {
        console.error('Error updating booking:', updateError);
        return { error: 'Failed to cancel booking' };
      }

      return {
        success: true,
        cancelled_booking_id: args.booking_id,
        refund_amount: booking.discounted_price,
        deal_title: booking.deals?.title
      };
    } catch (error) {
      console.error('Error in cancel_booking:', error);
      return { error: 'Failed to cancel booking' };
    }
  },

  get_booking_details: async (args: any) => {
    console.log('Getting booking details:', args);
    
    try {
      const { data: bookings, error } = await supabase
        .from('bookings')
        .select(`
          *, 
          deals (title, description, businesses(name, address, city))
        `)
        .eq('user_id', args.user_id)
        .order('created_at', { ascending: false })
        .limit(args.limit || 10);

      if (error) {
        console.error('Error fetching bookings:', error);
        return { error: 'Failed to fetch booking details' };
      }

      return {
        bookings: bookings || [],
        total_bookings: bookings?.length || 0
      };
    } catch (error) {
      console.error('Error in get_booking_details:', error);
      return { error: 'Failed to get booking details' };
    }
  },

  // ========== BUSINESS & DISCOVERY TOOLS ==========
  search_businesses: async (args: any) => {
    console.log('Searching businesses:', args);
    
    try {
      let query = supabase
        .from('businesses')
        .select(`
          id, name, description, address, city, state, latitude, longitude,
          phone, email, website, score, review_count,
          categories (name)
        `);

      if (args.category) {
        query = query.eq('categories.name', args.category);
      }
      
      if (args.city) {
        query = query.ilike('city', `%${args.city}%`);
      }
      
      if (args.query) {
        query = query.or(`name.ilike.%${args.query}%,description.ilike.%${args.query}%`);
      }

      const { data, error } = await query
        .order('score', { ascending: false })
        .limit(args.limit || 10);

      if (error) {
        return { error: error.message, businesses: [] };
      }

      return { 
        businesses: data || [],
        total_found: data?.length || 0
      };
    } catch (error) {
      console.error('Error in search_businesses:', error);
      return { error: 'Failed to search businesses', businesses: [] };
    }
  },

  get_recommendations: async (args: any) => {
    console.log('Getting recommendations for user:', args);
    
    try {
      // Get user preferences first
      const { data: preferences } = await supabase
        .from('user_preferences')
        .select('preferences')
        .eq('user_id', args.user_id)
        .single();

      // Get user's booking history for better recommendations
      const { data: bookingHistory } = await supabase
        .from('bookings')
        .select('deals(category_id, businesses(categories(name)))')
        .eq('user_id', args.user_id)
        .limit(10);

      // Find trending deals in user's preferred categories
      let query = supabase
        .from('deals')
        .select(`
          id, title, description, original_price, discounted_price,
          businesses (name, city, categories(name))
        `)
        .eq('status', 'published')
        .gte('end_date', new Date().toISOString());

      const { data: deals, error } = await query
        .order('created_at', { ascending: false })
        .limit(20);

      if (error) {
        return { error: error.message, recommendations: [] };
      }

      // Score deals based on user preferences and history
      const scoredDeals = deals?.map(deal => ({
        ...deal,
        recommendation_score: calculateRecommendationScore(deal, preferences, bookingHistory)
      })).sort((a, b) => b.recommendation_score - a.recommendation_score) || [];

      return {
        recommendations: scoredDeals.slice(0, args.limit || 5),
        recommendation_reason: 'Based on your preferences and booking history'
      };
    } catch (error) {
      console.error('Error in get_recommendations:', error);
      return { error: 'Failed to get recommendations', recommendations: [] };
    }
  },

  get_trending_deals: async (args: any) => {
    console.log('Getting trending deals:', args);
    
    try {
      // Get deals with high booking activity in the last 7 days
      const { data: trendingDeals, error } = await supabase
        .from('deals')
        .select(`
          id, title, description, original_price, discounted_price, discount_percentage,
          businesses (name, city),
          bookings (created_at)
        `)
        .eq('status', 'published')
        .gte('end_date', new Date().toISOString())
        .gte('bookings.created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: false })
        .limit(args.limit || 10);

      if (error) {
        return { error: error.message, trending: [] };
      }

      return {
        trending: trendingDeals || [],
        period: 'Last 7 days'
      };
    } catch (error) {
      console.error('Error in get_trending_deals:', error);
      return { error: 'Failed to get trending deals', trending: [] };
    }
  },

  get_nearby_deals: async (args: any) => {
    console.log('Getting nearby deals:', args);
    
    try {
      if (!args.latitude || !args.longitude) {
        return { error: 'Location coordinates required', deals: [] };
      }

      // Use PostGIS to find nearby deals within specified radius (default 5km)
      const radius = args.radius || 5000; // meters
      
      const { data, error } = await supabase.rpc('get_nearby_deals', {
        p_lat: args.latitude,
        p_lng: args.longitude,
        p_radius: radius,
        p_limit: args.limit || 10,
        p_date: args.date || new Date().toISOString().split('T')[0],
        p_time: args.time || null
      });

      if (error) {
        console.error('Error getting nearby deals:', error);
        return { error: error.message, deals: [] };
      }

      return {
        deals: data || [],
        search_location: { latitude: args.latitude, longitude: args.longitude },
        radius_km: radius / 1000
      };
    } catch (error) {
      console.error('Error in get_nearby_deals:', error);
      return { error: 'Failed to get nearby deals', deals: [] };
    }
  },

  // ========== USER & PREFERENCE TOOLS ==========
  get_user_preferences: async (args: any) => {
    console.log('Getting user preferences:', args);
    
    try {
      const { data, error } = await supabase
        .from('user_preferences')
        .select('preferences')
        .eq('user_id', args.user_id)
        .single();

      if (error && error.code !== 'PGRST116') { // Not found is ok
        return { error: error.message, preferences: {} };
      }

      return { 
        preferences: data?.preferences || {},
        user_id: args.user_id
      };
    } catch (error) {
      console.error('Error in get_user_preferences:', error);
      return { error: 'Failed to get user preferences', preferences: {} };
    }
  },

  get_user_profile: async (args: any) => {
    console.log('Getting user profile:', args);
    
    try {
      // Get user metadata from auth.users
      const { data: authUser, error: authError } = await supabase.auth.admin.getUserById(args.user_id);
      
      if (authError) {
        return { error: 'User not found' };
      }

      // Get additional profile data
      const { data: preferences } = await supabase
        .from('user_preferences')
        .select('preferences')
        .eq('user_id', args.user_id)
        .single();

      const profile = {
        id: authUser.user.id,
        email: authUser.user.email,
        created_at: authUser.user.created_at,
        metadata: authUser.user.user_metadata,
        preferences: preferences?.preferences || {}
      };

      return { profile };
    } catch (error) {
      console.error('Error in get_user_profile:', error);
      return { error: 'Failed to get user profile' };
    }
  },

  // ========== BUSINESS INTELLIGENCE TOOLS ==========
  get_business_metrics: async (args: any) => {
    console.log('Getting business metrics:', args);
    
    try {
      const periodDays = args.period_days || 30;
      const startDate = new Date(Date.now() - periodDays * 24 * 60 * 60 * 1000).toISOString();

      // Get booking metrics
      const { data: bookings } = await supabase
        .from('bookings')
        .select('id, status, created_at, discounted_price')
        .gte('created_at', startDate);

      // Get deal metrics
      const { data: deals } = await supabase
        .from('deals')
        .select('id, status, created_at')
        .gte('created_at', startDate);

      // Get business metrics
      const { data: businesses } = await supabase
        .from('businesses')
        .select('id, created_at');

      const totalRevenue = bookings?.reduce((sum, booking) => 
        sum + (parseFloat(booking.discounted_price) || 0), 0) || 0;

      const confirmedBookings = bookings?.filter(b => b.status === 'confirmed').length || 0;
      const cancelledBookings = bookings?.filter(b => b.status === 'cancelled').length || 0;

      return {
        period_days: periodDays,
        metrics: {
          total_bookings: bookings?.length || 0,
          confirmed_bookings: confirmedBookings,
          cancelled_bookings: cancelledBookings,
          cancellation_rate: bookings?.length ? (cancelledBookings / bookings.length * 100).toFixed(1) : 0,
          total_revenue: totalRevenue.toFixed(2),
          active_deals: deals?.filter(d => d.status === 'published').length || 0,
          total_businesses: businesses?.length || 0,
          average_booking_value: bookings?.length ? (totalRevenue / bookings.length).toFixed(2) : 0
        }
      };
    } catch (error) {
      console.error('Error in get_business_metrics:', error);
      return { error: 'Failed to get business metrics' };
    }
  },

  // ========== SUPPORT TOOLS ==========
  send_notification: async (args: any) => {
    console.log('Sending notification:', args);
    
    // In a real implementation, this would integrate with push notification service
    return {
      success: true,
      notification_id: crypto.randomUUID(),
      message: args.message,
      recipient: args.user_id,
      type: args.type || 'info'
    };
  },

  escalate_issue: async (args: any) => {
    console.log('Escalating issue:', args);
    
    // In a real implementation, this would create a support ticket
    return {
      success: true,
      ticket_id: `TICKET_${Date.now()}`,
      issue: args.issue,
      priority: args.priority || 'medium',
      status: 'escalated'
    };
  }
};

// ============================================================================
// UTILITY FUNCTIONS - Supporting functions for business logic
// ============================================================================

/**
 * Calculate relevance score for a deal based on search parameters
 */
function calculateDealRelevance(deal: any, searchParams: any): number {
  let score = 0.5; // Base score

  // Boost score based on discount percentage
  if (deal.discount_percentage > 50) score += 0.3;
  else if (deal.discount_percentage > 30) score += 0.2;
  else if (deal.discount_percentage > 15) score += 0.1;

  // Boost score for exact matches in title/description
  if (searchParams.query) {
    const query = searchParams.query.toLowerCase();
    if (deal.title?.toLowerCase().includes(query)) score += 0.2;
    if (deal.description?.toLowerCase().includes(query)) score += 0.1;
  }

  // Boost score for recently created deals
  const dealAge = Date.now() - new Date(deal.created_at).getTime();
  const daysSinceCreated = dealAge / (1000 * 60 * 60 * 24);
  if (daysSinceCreated < 7) score += 0.1;

  return Math.min(score, 1.0); // Cap at 1.0
}

/**
 * Calculate recommendation score based on user preferences and history
 */
function calculateRecommendationScore(deal: any, preferences: any, bookingHistory: any[]): number {
  let score = 0.5; // Base score

  // Boost based on user's category preferences
  const userCategories = bookingHistory?.map(b => 
    b.deals?.businesses?.categories?.name
  ).filter(Boolean) || [];

  if (userCategories.includes(deal.businesses?.categories?.name)) {
    score += 0.3;
  }

  // Boost based on discount percentage
  if (deal.discount_percentage > 40) score += 0.2;

  return Math.min(score, 1.0);
}

// ============================================================================
// OPENAI INTEGRATION - Production AI agent responses
// ============================================================================

/**
 * Generate intelligent agent response using OpenAI
 */
async function generateOpenAIResponse(
  agentType: string, 
  message: string, 
  context: AgentState,
  toolResults: ToolResult[] = []
): Promise<string> {
  if (!openaiApiKey) {
    return generateFallbackResponse(agentType, message, toolResults);
  }

  try {
    const config = AGENT_CONFIGS[agentType as keyof typeof AGENT_CONFIGS];
    const messages = [
      {
        role: 'system',
        content: config.systemPrompt + `\n\nContext: ${JSON.stringify(context.context || {})}`
      },
      ...context.conversation_history?.slice(-5) || [], // Last 5 messages for context
      {
        role: 'user',
        content: message
      }
    ];

    // Add tool results if any
    if (toolResults.length > 0) {
      const toolContext = toolResults.map(tr => 
        `${tr.name}: ${JSON.stringify(tr.result)}`
      ).join('\n');
      
      messages.push({
        role: 'system',
        content: `Tool results available:\n${toolContext}`
      });
    }

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: config.model,
        messages,
        max_tokens: 1000,
        temperature: 0.7,
        tools: config.tools.map(toolName => ({
          type: 'function',
          function: {
            name: toolName,
            description: getToolDescription(toolName),
            parameters: getToolParameters(toolName)
          }
        }))
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`);
    }

    const data: OpenAIResponse = await response.json();
    return data.choices[0]?.message?.content || generateFallbackResponse(agentType, message, toolResults);

  } catch (error) {
    console.error('OpenAI API error:', error);
    return generateFallbackResponse(agentType, message, toolResults);
  }
}

/**
 * Fallback response generator when OpenAI is unavailable
 */
function generateFallbackResponse(agentType: string, message: string, toolResults: ToolResult[]): string {
  const lowerMessage = message.toLowerCase();
  
  switch (agentType) {
    case 'router':
      const routeResult = toolResults.find(r => r.name === 'route_to_agent')?.result;
      if (routeResult) {
        return `Ho analizzato la tua richiesta e ho determinato che l'agente ${routeResult.agent} è il più adatto per aiutarti. ${routeResult.reasoning}`;
      }
      return 'Sto analizzando la tua richiesta per indirizzarti all\'agente più appropriato...';
      
    case 'booking':
      const dealResults = toolResults.find(r => r.name === 'search_deals')?.result;
      if (dealResults?.deals?.length > 0) {
        const dealsList = dealResults.deals.slice(0, 3).map((deal: any) => 
          `• ${deal.title}: €${deal.discounted_price} (era €${deal.original_price}) presso ${deal.businesses?.name || 'N/A'}`
        ).join('\n');
        return `Ecco alcuni deal che potrebbero interessarti:\n\n${dealsList}\n\nVuoi che ti aiuti a prenotare uno di questi o cerchi qualcosa di specifico?`;
      }
      
      if (lowerMessage.includes('prenot') || lowerMessage.includes('book')) {
        return 'Perfetto! Sono qui per aiutarti con le prenotazioni. Dimmi cosa stai cercando - che tipo di servizio, in quale città, e per quando?';
      }
      
      return 'Sono l\'assistente prenotazioni di CatchUp! Posso aiutarti a trovare e prenotare deal fantastici, gestire le tue prenotazioni esistenti, o rispondere a qualsiasi domanda sui booking. Come posso aiutarti oggi?';
      
    case 'discovery':
      const recommendations = toolResults.find(r => r.name === 'get_recommendations')?.result;
      if (recommendations?.recommendations?.length > 0) {
        const recList = recommendations.recommendations.slice(0, 3).map((deal: any) => 
          `• ${deal.title} - Sconto del ${deal.discount_percentage}%`
        ).join('\n');
        return `Basandomi sui tuoi interessi, ti consiglio:\n\n${recList}\n\nTi interessano altri tipi di deal o vuoi esplorare categorie specifiche?`;
      }
      
      return 'Ciao! Sono l\'assistente scoperta di CatchUp. Ti aiuto a trovare deal incredibili e business interessanti. Dimmi cosa ti piace o cosa stai cercando e ti darò suggerimenti personalizzati!';
      
    case 'support':
      if (lowerMessage.includes('problema') || lowerMessage.includes('errore')) {
        return 'Mi dispiace che tu stia avendo problemi. Sono qui per aiutarti a risolverli. Puoi descrivermi nel dettaglio cosa sta succedendo? Così potrò fornirti la soluzione migliore.';
      }
      
      return 'Ciao! Sono l\'assistente supporto di CatchUp. Sono qui per aiutarti con qualsiasi domanda o difficoltà tu possa avere sulla piattaforma. Come posso assisterti oggi?';
      
    case 'business_intelligence':
      const metrics = toolResults.find(r => r.name === 'get_business_metrics')?.result;
      if (metrics?.metrics) {
        return `Ecco un'overview delle metriche di CatchUp:\n\n📊 **Ultimi ${metrics.period_days} giorni:**\n• Prenotazioni totali: ${metrics.metrics.total_bookings}\n• Prenotazioni confermate: ${metrics.metrics.confirmed_bookings}\n• Fatturato: €${metrics.metrics.total_revenue}\n• Deal attivi: ${metrics.metrics.active_deals}\n\nVuoi approfondimenti su qualche metrica specifica?`;
      }
      
      return 'Ciao! Sono l\'assistente business intelligence di CatchUp. Posso fornirti analisi dettagliate, report e insights sui dati della piattaforma. Quali metriche o analisi ti interessano?';
      
    default:
      return 'Ciao! Come posso aiutarti oggi con CatchUp?';
  }
}

/**
 * Get tool description for OpenAI function calling
 */
function getToolDescription(toolName: string): string {
  const descriptions: Record<string, string> = {
    route_to_agent: 'Route user to appropriate specialized agent',
    search_deals: 'Search for available deals with filters',
    check_availability: 'Check availability for specific deal and time',
    create_booking: 'Create a new booking reservation',
    cancel_booking: 'Cancel an existing booking',
    get_booking_details: 'Get user\'s booking history and details',
    search_businesses: 'Search for businesses by criteria',
    get_recommendations: 'Get personalized deal recommendations',
    get_trending_deals: 'Get currently trending deals',
    get_nearby_deals: 'Get deals near user location',
    get_user_preferences: 'Get user preferences and settings',
    get_user_profile: 'Get user profile information',
    get_business_metrics: 'Get business analytics and metrics',
    send_notification: 'Send notification to user',
    escalate_issue: 'Escalate issue to human support'
  };
  
  return descriptions[toolName] || 'Tool description not available';
}

/**
 * Get tool parameters for OpenAI function calling
 */
function getToolParameters(toolName: string): any {
  const parameters: Record<string, any> = {
    route_to_agent: {
      type: 'object',
      properties: {
        agent: { type: 'string', enum: ['booking', 'discovery', 'support', 'business_intelligence'] },
        reasoning: { type: 'string' }
      },
      required: ['agent', 'reasoning']
    },
    search_deals: {
      type: 'object',
      properties: {
        query: { type: 'string' },
        category: { type: 'string' },
        city: { type: 'string' },
        max_price: { type: 'number' },
        limit: { type: 'number' }
      }
    },
    create_booking: {
      type: 'object',
      properties: {
        deal_id: { type: 'string' },
        user_id: { type: 'string' },
        booking_date: { type: 'string' },
        booking_time: { type: 'string' }
      },
      required: ['deal_id', 'user_id', 'booking_date', 'booking_time']
    }
    // Add more parameter definitions as needed
  };
  
  return parameters[toolName] || { type: 'object', properties: {} };
}

// ============================================================================
// AGENT ORCHESTRATOR - Production LangGraph-like workflow engine
// ============================================================================

class ProductionAgentOrchestrator {
  private state: AgentState;
  private executionLog: Array<{ timestamp: string; action: string; details: any }> = [];
  
  constructor(initialState: AgentState) {
    this.state = initialState;
    this.log('orchestrator_initialized', { initial_agent: initialState.current_agent });
  }

  /**
   * Main routing logic - determines and executes appropriate agent
   */
  async routeMessage(message: string, preferredAgent?: string): Promise<any> {
    this.log('route_message_start', { message, preferredAgent });
    
    try {
      // If specific agent requested, route directly (unless it's router)
      if (preferredAgent && preferredAgent !== 'router' && AGENT_CONFIGS[preferredAgent as keyof typeof AGENT_CONFIGS]) {
        this.log('direct_agent_routing', { target_agent: preferredAgent });
        return await this.executeAgent(preferredAgent, message);
      }
      
      // Use router agent to determine best agent
      this.log('router_agent_execution', {});
      const routerResponse = await this.executeAgent('router', message);
      
      // Extract routing decision from router response
      const routingDecision = routerResponse.tool_results?.find((tr: ToolResult) => tr.name === 'route_to_agent');
      
      if (routingDecision?.result?.agent && routingDecision.result.agent !== 'router') {
        const targetAgent = routingDecision.result.agent;
        this.log('router_decision', { target_agent: targetAgent, reasoning: routingDecision.result.reasoning });
        
        // Execute target agent with original message
        const agentResponse = await this.executeAgent(targetAgent, message);
        
        // Combine router reasoning with agent response
        agentResponse.routing_info = {
          router_reasoning: routingDecision.result.reasoning,
          selected_agent: targetAgent
        };
        
        return agentResponse;
      }
      
      // Fallback to router response if no routing decision
      return routerResponse;
      
    } catch (error) {
      this.log('route_message_error', { error: error.message });
      throw error;
    }
  }

  /**
   * Execute specific agent with full context and tool integration
   */
  async executeAgent(agentType: string, message: string): Promise<any> {
    this.log('execute_agent_start', { agent_type: agentType, message });
    
    const config = AGENT_CONFIGS[agentType as keyof typeof AGENT_CONFIGS];
    if (!config) {
      throw new Error(`Unknown agent type: ${agentType}`);
    }

    try {
      // Update current agent in state
      this.state.current_agent = agentType;
      
      // Determine and execute required tools based on message and agent type
      const toolResults = await this.executeAgentTools(agentType, message);
      
      // Generate intelligent response using OpenAI or fallback
      const responseContent = await generateOpenAIResponse(
        agentType, 
        message, 
        this.state, 
        toolResults
      );
      
      // Update conversation state
      this.state.messages.push(
        { role: 'user', content: message },
        { role: 'assistant', content: responseContent }
      );
      
      const response = {
        agent_type: agentType,
        content: responseContent,
        tool_calls: toolResults.map(tr => ({ name: tr.name, arguments: {} })),
        tool_results: toolResults,
        metadata: {
          processed_at: new Date().toISOString(),
          agent_config: config.name,
          execution_log: this.executionLog.slice(-5), // Last 5 log entries
          state_update: {
            current_agent: agentType,
            message_count: this.state.messages.length
          }
        }
      };
      
      this.log('execute_agent_success', { 
        agent_type: agentType, 
        tools_executed: toolResults.length,
        response_length: responseContent.length 
      });
      
      return response;
      
    } catch (error) {
      this.log('execute_agent_error', { agent_type: agentType, error: error.message });
      throw error;
    }
  }

  /**
   * Execute relevant tools based on agent type and message content
   */
  private async executeAgentTools(agentType: string, message: string): Promise<ToolResult[]> {
    const toolResults: ToolResult[] = [];
    const lowerMessage = message.toLowerCase();
    
    try {
      switch (agentType) {
        case 'router':
          // Router always determines target agent
          const targetAgent = this.determineTargetAgent(message);
          const routeResult = TOOLS.route_to_agent({
            agent: targetAgent,
            reasoning: `Message content analysis suggests ${targetAgent} agent is most appropriate`
          });
          toolResults.push({ name: 'route_to_agent', result: routeResult });
          break;

        case 'booking':
          // Search deals if user mentions deals or is looking for something to book
          if (lowerMessage.includes('deal') || lowerMessage.includes('cerca') || lowerMessage.includes('find')) {
            const searchResult = await TOOLS.search_deals({ 
              query: message, 
              limit: 5,
              user_id: this.state.user_id 
            });
            toolResults.push({ name: 'search_deals', result: searchResult });
          }
          
          // Get user bookings if asking about existing reservations
          if (lowerMessage.includes('prenotaz') || lowerMessage.includes('booking') || lowerMessage.includes('mie')) {
            const bookingsResult = await TOOLS.get_booking_details({ 
              user_id: this.state.user_id,
              limit: 10 
            });
            toolResults.push({ name: 'get_booking_details', result: bookingsResult });
          }
          break;

        case 'discovery':
          // Get personalized recommendations
          const recommendationsResult = await TOOLS.get_recommendations({ 
            user_id: this.state.user_id,
            limit: 5 
          });
          toolResults.push({ name: 'get_recommendations', result: recommendationsResult });
          
          // Search for specific deals if query provided
          if (lowerMessage.includes('cerca') || lowerMessage.includes('find') || lowerMessage.includes('deal')) {
            const searchResult = await TOOLS.search_deals({ 
              query: message,
              limit: 8,
              user_id: this.state.user_id 
            });
            toolResults.push({ name: 'search_deals', result: searchResult });
          }
          break;

        case 'support':
          // Get user profile for account-related queries
          if (lowerMessage.includes('account') || lowerMessage.includes('profilo') || lowerMessage.includes('impostaz')) {
            const profileResult = await TOOLS.get_user_profile({ user_id: this.state.user_id });
            toolResults.push({ name: 'get_user_profile', result: profileResult });
          }
          break;

        case 'business_intelligence':
          // Always get business metrics for BI queries
          const metricsResult = await TOOLS.get_business_metrics({ period_days: 30 });
          toolResults.push({ name: 'get_business_metrics', result: metricsResult });
          break;
      }
      
    } catch (error) {
      this.log('tool_execution_error', { agent_type: agentType, error: error.message });
      // Continue with partial results rather than failing completely
    }
    
    return toolResults;
  }

  /**
   * Intelligent agent routing based on message analysis
   */
  private determineTargetAgent(message: string): string {
    const lowerMessage = message.toLowerCase();
    
    // Booking keywords (Italian and English)
    const bookingKeywords = ['prenot', 'book', 'riservaz', 'reservation', 'cancel', 'disdire', 'modifica'];
    if (bookingKeywords.some(keyword => lowerMessage.includes(keyword))) {
      return 'booking';
    }
    
    // Discovery keywords
    const discoveryKeywords = ['trova', 'cerca', 'find', 'search', 'deal', 'sconto', 'offerta', 'consiglia'];
    if (discoveryKeywords.some(keyword => lowerMessage.includes(keyword))) {
      return 'discovery';
    }
    
    // Business Intelligence keywords
    const biKeywords = ['analytic', 'metric', 'report', 'data', 'statistich', 'performance', 'vendite', 'fatturato'];
    if (biKeywords.some(keyword => lowerMessage.includes(keyword))) {
      return 'business_intelligence';
    }
    
    // Support keywords
    const supportKeywords = ['aiuto', 'help', 'problema', 'problem', 'issue', 'errore', 'error', 'come', 'how'];
    if (supportKeywords.some(keyword => lowerMessage.includes(keyword))) {
      return 'support';
    }
    
    // Default to discovery for general queries
    return 'discovery';
  }

  /**
   * Internal logging for debugging and monitoring
   */
  private log(action: string, details: any) {
    this.executionLog.push({
      timestamp: new Date().toISOString(),
      action,
      details
    });
    
    console.log(`[AgentOrchestrator] ${action}:`, details);
  }
}

// ============================================================================
// HTTP REQUEST HANDLER - Main entry point for agent requests
// ============================================================================

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  const startTime = Date.now();
  
  try {
    const requestBody = await req.json();
    const { conversation_id, message, preferred_agent, user_id } = requestBody;
    
    console.log('🚀 LangGraph Agent Request:', { 
      conversation_id, 
      message: message?.substring(0, 100) + '...', 
      preferred_agent,
      user_id,
      timestamp: new Date().toISOString()
    });

    // Validate required fields
    if (!conversation_id || !message) {
      throw new Error('Missing required fields: conversation_id and message are required');
    }

    // Fetch conversation context
    const { data: conversation, error: convError } = await supabase
      .from('agent_conversations')
      .select('*')
      .eq('id', conversation_id)
      .single();

    if (convError) {
      console.error('❌ Error fetching conversation:', convError);
      throw new Error(`Conversation not found: ${convError.message}`);
    }

    // Get recent conversation history for context
    const { data: recentMessages, error: messagesError } = await supabase
      .from('agent_messages')
      .select('role, content, created_at')
      .eq('conversation_id', conversation_id)
      .order('created_at', { ascending: false })
      .limit(10);

    if (messagesError) {
      console.warn('⚠️ Could not fetch message history:', messagesError);
    }

    // Build comprehensive agent state
    const agentState: AgentState = {
      messages: [],
      current_agent: conversation.current_agent_type || 'router',
      context: conversation.state || {},
      user_preferences: {},
      user_id: user_id || conversation.user_id,
      conversation_history: (recentMessages || []).reverse().map(msg => ({
        role: msg.role as 'user' | 'assistant' | 'system',
        content: msg.content
      }))
    };

    // Create orchestrator and process the request
    const orchestrator = new ProductionAgentOrchestrator(agentState);
    const agentResponse = await orchestrator.routeMessage(message, preferred_agent);

    console.log('✅ Agent Response Generated:', { 
      agent_type: agentResponse.agent_type,
      tools_used: agentResponse.tool_results?.length || 0,
      response_length: agentResponse.content?.length || 0,
      processing_time: Date.now() - startTime
    });

    // Save user message to database
    const { error: userMessageError } = await supabase
      .from('agent_messages')
      .insert({
        conversation_id,
        role: 'user',
        content: message,
        metadata: { 
          preferred_agent,
          user_agent: req.headers.get('user-agent'),
          timestamp: new Date().toISOString()
        }
      });

    if (userMessageError) {
      console.error('⚠️ Error saving user message:', userMessageError);
    }

    // Save agent response to database
    const { error: agentMessageError } = await supabase
      .from('agent_messages')
      .insert({
        conversation_id,
        agent_type: agentResponse.agent_type,
        role: 'assistant',
        content: agentResponse.content,
        metadata: agentResponse.metadata || {},
        tool_calls: agentResponse.tool_calls || [],
        tool_results: agentResponse.tool_results || []
      });

    if (agentMessageError) {
      console.error('❌ Error saving agent response:', agentMessageError);
      // Don't throw here - we still want to return the response
    }

    // Update conversation state
    const { error: updateError } = await supabase
      .from('agent_conversations')
      .update({
        current_agent_type: agentResponse.agent_type,
        state: { 
          ...conversation.state, 
          last_response: agentResponse,
          last_activity: new Date().toISOString()
        },
        updated_at: new Date().toISOString()
      })
      .eq('id', conversation_id);

    if (updateError) {
      console.error('⚠️ Error updating conversation:', updateError);
    }

    // Prepare successful response
    const response = {
      success: true,
      response: agentResponse,
      conversation_id,
      agent_type: agentResponse.agent_type,
      processing_time_ms: Date.now() - startTime,
      metadata: {
        tools_executed: agentResponse.tool_results?.length || 0,
        routing_info: agentResponse.routing_info,
        timestamp: new Date().toISOString()
      }
    };

    console.log('📤 Sending response:', { 
      success: true, 
      agent: agentResponse.agent_type, 
      processing_time: Date.now() - startTime + 'ms' 
    });

    return new Response(
      JSON.stringify(response),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    );

  } catch (error) {
    const processingTime = Date.now() - startTime;
    console.error('💥 LangGraph Agent Error:', {
      error: error.message,
      stack: error.stack,
      processing_time: processingTime
    });
    
    // Return structured error response
    const errorResponse = {
      success: false,
      error: {
        message: error.message || 'Internal server error',
        type: error.name || 'UnknownError',
        timestamp: new Date().toISOString(),
        processing_time_ms: processingTime
      }
    };
    
    return new Response(
      JSON.stringify(errorResponse),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    );
  }
});