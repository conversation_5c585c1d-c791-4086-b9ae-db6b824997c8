import React from 'react';
import { PushNotificationSettings } from '@/components/pwa/PushNotificationSettings';
import Header from '@/components/toolbars/Header';
import BottomNavigationBar from '@/components/toolbars/BottomNavigationBar';

const PushNotificationTest: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header title="Push Notifications Test" isBusiness={false} />
      
      <main className="pt-16 pb-20 px-4">
        <div className="max-w-2xl mx-auto space-y-6">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-800 mb-2">
              Push Notifications
            </h1>
            <p className="text-gray-600">
              Test push notification functionality
            </p>
          </div>
          
          <PushNotificationSettings />
          
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">
              How it works:
            </h2>
            <div className="space-y-3 text-sm text-gray-600">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-blue-600 font-semibold text-xs">1</span>
                </div>
                <p>Click "Enable Notifications" to request permission</p>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-blue-600 font-semibold text-xs">2</span>
                </div>
                <p>Click "Subscribe" to create a push subscription</p>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-blue-600 font-semibold text-xs">3</span>
                </div>
                <p>Click "Send Test Notification" to test local notifications</p>
              </div>
            </div>
          </div>
        </div>
      </main>
      
      <BottomNavigationBar isBusiness={false} />
    </div>
  );
};

export default PushNotificationTest; 