import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import Navigation from "@/components/toolbars/BottomNavigationBar";
import { MapPin } from "lucide-react";
import { useNavigate } from "react-router-dom";
import Header from "@/components/toolbars/Header";
import { useLocationManagement } from "@/hooks/location/useLocationManagement";
import { useAuth } from "@/hooks/auth/useAuth";
import { useBusinessMode } from "@/hooks/useBusinessMode";
import { toast } from "sonner";
import { APIProvider, Map } from '@vis.gl/react-google-maps';
import { RealEstateListing } from "@/types/types";
import { loadRealEstateListing } from "./load-real-estate-listing";
import { CustomAdvancedMarker } from "@/components/map/custom-advanced-marker";
import { LocationDateTimeBar } from "@/components/search/LocationDateTimeBar";
import './style.css';

interface Business {
  id: string;
  name: string;
  latitude: number | null;
  longitude: number | null;
  deal_count: number | null;
  deals?: any[];
}

const MapNew = () => {
  const navigate = useNavigate();
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [apiKey, setApiKey] = useState<string>("");
  const [currentLocation, setCurrentLocation] = useState("Posizione Attuale");
  const { isLoading: isLocationLoading } = useLocationManagement();
  const { userDetails } = useAuth();
  const { isBusinessMode } = useBusinessMode();
  
  // Verifichiamo se la posizione è abilitata
  const locationEnabled = userDetails?.location_enabled ?? true;


  const [realEstateListing, setRealEstateListing] =
    useState<RealEstateListing | null>(null);

    useEffect(() => {
        void loadRealEstateListing().then(data => {
          setRealEstateListing(data);
          console.log("Real estate data loaded:", data);
        }).catch(error => {
          console.error("Error loading real estate data:", error);
        });
      }, []);
  useEffect(() => {
    // Se la posizione non è abilitata, mostriamo un messaggio all'utente
    if (!locationEnabled) {
      toast.info("Posizione disabilitata nelle impostazioni. Alcune funzionalità potrebbero essere limitate.");
    }
    
    const fetchBusinesses = async () => {
      // Modifichiamo la query per includere anche le offerte
      const { data, error } = await supabase
        .from('businesses_with_counts')
        .select(`
          id, 
          name, 
          latitude, 
          longitude,
          deal_count,
          deals:deals(*)
        `)
        .not('latitude', 'is', null)
        .not('longitude', 'is', null)
        .eq('deals.status', 'published');

      if (error) {
        console.error('Errore nel caricamento delle attività:', error);
        return;
      }
      
      setBusinesses(data || []);
    };

    const getGoogleMapsKey = async () => {
      const { data: { GOOGLE_MAPS_API_KEY }, error } = await supabase
        .functions.invoke('get-google-maps-key');
      if (error) {
        console.error('Errore nel recupero della chiave API:', error);
        // Use a fallback key for development if available in environment
        const fallbackKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;
        if (fallbackKey) {
          console.log('Using fallback API key');
          setApiKey(fallbackKey);
        }
        return;
      }
      setApiKey(GOOGLE_MAPS_API_KEY);
      console.log("API key retrieved successfully");
    };

    fetchBusinesses();
    getGoogleMapsKey();
  }, [locationEnabled]);

  const handleLocationChange = (location: string, place?: google.maps.places.Place) => {
    setCurrentLocation(location);
    
    if (place && place.location) {
      console.log("Nuova posizione:", location, place.location.toJSON());
      // TODO: Update map center to the new location
      // This will be implemented when we add map state management
    } else {
      console.log("Nuova posizione (testo):", location);
    }
  };

  if (!apiKey || isLocationLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header title="Mappa" isBusiness={isBusinessMode} />
        <div className="flex items-center justify-center h-[calc(100vh-120px)]">
          <div className="animate-spin">
            <MapPin className="h-8 w-8 text-brand-primary" />
          </div>
        </div>
        <Navigation isBusiness={isBusinessMode} />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header title="Mappa1" isBusiness={isBusinessMode} />
      
      {/* Location, Date, Time toolbar */}
      <LocationDateTimeBar 
        location={currentLocation}
        onLocationChange={handleLocationChange}
      />
      
      <div className="h-[calc(100vh-160px)]">

      <div className="advanced-marker-example">
      <APIProvider apiKey={apiKey} libraries={['marker', 'places']}>
        <Map
          mapId={'bf51a910020fa25a'}
          defaultZoom={5}
          defaultCenter={{lat: 47.53, lng: -122.34}}
          gestureHandling={'greedy'}
          disableDefaultUI
         >
          {/* advanced marker with html-content */}
          {realEstateListing && (
            <CustomAdvancedMarker realEstateListing={realEstateListing} />
          )}
        </Map>

     
      </APIProvider>
    </div>

        {/* <MapView 
          businesses={businesses}
          onDealClick={(dealId) => navigate(`/deal/${dealId}`)}
          locationEnabled={locationEnabled}
        /> */}
      </div>

      <Navigation isBusiness={isBusinessMode} />
    </div>
  );
};

export default MapNew;
