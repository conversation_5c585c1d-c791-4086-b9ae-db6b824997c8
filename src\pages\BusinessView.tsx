
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { Plus } from "lucide-react";
import BottomNavigationBar from "@/components/toolbars/BottomNavigationBar";
import { BusinessHeader } from "@/components/business/BusinessHeader";
import { BusinessImageCarousel } from "@/components/business/BusinessImageCarousel";
import { BusinessInfoForm } from "@/components/business/BusinessInfoForm";
import { BusinessStats } from "@/components/business/BusinessStats";
import { useBusinessDashboard } from "@/hooks/useBusinessDashboard";
import BusinessDashboardSkeleton from "@/components/business/BusinessDashboardSkeleton";
import { useBusinessMode } from "@/hooks/useBusinessMode";

const BusinessView = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { isBusinessMode } = useBusinessMode();
  const {
    business,
    dealsCount,
    draftDealsCount,
    currentImageIndex,
    isEditing,
    isSaving,
    editForm,
    setCurrentImageIndex,
    setIsEditing,
    setEditForm,
    handleSave,
    handleFormChange,
  } = useBusinessDashboard(id);

  if (!business) {
    return <BusinessDashboardSkeleton />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <BusinessHeader businessName={business.name} />

      <main className="pt-16 pb-20 px-4">
        <section className="mt-6">
          <BusinessImageCarousel
            photos={business.photos || []}
            businessName={business.name}
            currentImageIndex={currentImageIndex}
            onNext={() => setCurrentImageIndex((prev) =>
              prev === (business.photos?.length || 0) - 1 ? 0 : prev + 1
            )}
            onPrevious={() => setCurrentImageIndex((prev) =>
              prev === 0 ? (business.photos?.length || 0) - 1 : prev - 1
            )}
            onSelectImage={setCurrentImageIndex}
          />

          <BusinessInfoForm
            business={business}
            isEditing={isEditing}
            isSaving={isSaving}
            editForm={editForm}
            onEdit={() => setIsEditing(true)}
            onCancel={() => {
              setEditForm(business);
              setIsEditing(false);
            }}
            onSave={handleSave}
            onFormChange={handleFormChange}
          />
        </section>

        <BusinessStats dealsCount={dealsCount} draftDealsCount={draftDealsCount} businessId={id} />

        <section className="mt-8">
          <button
            onClick={() => navigate(`/business/${id}/create-deal`)}
            className="w-full bg-brand-primary text-white py-4 rounded-xl font-semibold shadow-lg shadow-brand-primary/20 flex items-center justify-center gap-2"
          >
            <Plus className="h-5 w-5" />
            Crea Nuova Offerta
          </button>
        </section>
      </main>

      <BottomNavigationBar isBusiness={isBusinessMode} />
    </div>
  );
};

export default BusinessView;
