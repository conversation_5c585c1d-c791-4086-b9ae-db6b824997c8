import { useState, useEffect } from "react";
import { MessageCircle, Filter, Compass, Users, Camera, Cloud, Calendar } from "lucide-react";
import { format } from "date-fns";
import { it } from "date-fns/locale";
import { useAuth } from "@/hooks/auth/useAuth";
import { useNotificationCount } from "@/hooks/useNotificationCount";
import { useNavigate } from "react-router-dom";

interface Tab {
  id: string;
  label: string;
  badge?: number;
  icon: React.ComponentType<{ className?: string }>;
}

interface HeaderWithTabsProps {
  onSearchClick?: () => void;
  showSearchButton?: boolean;
  activeFiltersCount?: number;
  hasActiveFilters?: boolean;
  tabs?: Tab[];
  activeTab?: string;
  onTabChange?: (tabId: string) => void;
}

const HeaderWithTabs = ({ 
  onSearchClick, 
  showSearchButton = false, 
  activeFiltersCount = 0,
  hasActiveFilters = false,
  tabs = [],
  activeTab,
  onTabChange
}: HeaderWithTabsProps) => {
  const { userDetails, isAuthenticated } = useAuth();
  const { totalCount } = useNotificationCount();
  const navigate = useNavigate();
  
  const [isScrolled, setIsScrolled] = useState(false);
  const [hideTabBar, setHideTabBar] = useState(false);

  useEffect(() => {
    let lastScrollY = 0;
    let scrollTimeout: NodeJS.Timeout;
    let isAnimating = false;
    
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      const documentHeight = document.documentElement.scrollHeight;
      const windowHeight = window.innerHeight;
      const maxScroll = documentHeight - windowHeight;
      
      // Don't hide tabs if page is too short to scroll meaningfully
      if (maxScroll < 200) {
        setHideTabBar(false);
        setIsScrolled(currentScrollY > 10);
        return;
      }
      
      // Prevent overlapping animations
      if (isAnimating) return;
      
      // Clear existing timeout
      clearTimeout(scrollTimeout);
      
      const scrollDifference = currentScrollY - lastScrollY;
      const scrollDirection = scrollDifference > 0 ? 'down' : 'up';
      
      // Use larger threshold and hysteresis to prevent flickering
      const hideThreshold = 120;
      const showThreshold = 80;
      const minScrollMovement = 25;
      
      // Only react to meaningful scroll movements
      if (Math.abs(scrollDifference) > minScrollMovement) {
        if (scrollDirection === 'down' && currentScrollY > hideThreshold) {
          // Scrolling down past threshold - hide tabs
          if (!hideTabBar) {
            isAnimating = true;
            setHideTabBar(true);
            setTimeout(() => { isAnimating = false; }, 300);
          }
        } else if (scrollDirection === 'up' && currentScrollY < showThreshold) {
          // Scrolling up below threshold - show tabs
          if (hideTabBar) {
            isAnimating = true;
            setHideTabBar(false);
            setTimeout(() => { isAnimating = false; }, 300);
          }
        }
        
        lastScrollY = currentScrollY;
      }
      
      // Always update scroll state for styling
      setIsScrolled(currentScrollY > 10);
      
      // Set timeout to handle end of scrolling
      scrollTimeout = setTimeout(() => {
        // If we're near the top when scrolling stops, always show tabs
        if (currentScrollY <= 60 && hideTabBar) {
          isAnimating = true;
          setHideTabBar(false);
          setTimeout(() => { isAnimating = false; }, 300);
        }
      }, 150);
    };

    // Use passive listener for better performance and throttle
    let ticking = false;
    const throttledHandler = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', throttledHandler, { passive: true });
    
    return () => {
      window.removeEventListener('scroll', throttledHandler);
      clearTimeout(scrollTimeout);
    };
  }, [hideTabBar]);

  const handleNotificationClick = () => {
    navigate('/conversations?tab=notifications');
  };

  const formatDateInItalian = () => {
    const formatted = format(new Date(), "EEEE, d MMMM", { locale: it });
    const parts = formatted.split(", ");
    const weekday = parts[0].charAt(0).toUpperCase() + parts[0].slice(1);
    const dayAndMonth = parts[1].split(" ");
    const day = dayAndMonth[0];
    const month = dayAndMonth[1].charAt(0).toUpperCase() + dayAndMonth[1].slice(1);
    return `${weekday}, ${day} ${month}`;
  };

  const getTimeBasedGreeting = () => {
    const hour = new Date().getHours();
    if (hour >= 5 && hour < 12) {
      return "Buongiorno";
    } else if (hour >= 12 && hour < 18) {
      return "Buon pomeriggio";
    } else if (hour >= 18 && hour < 22) {
      return "Buona sera";
    } else {
      return "Buona notte";
    }
  };

  const firstName = userDetails?.first_name || "Utente";

  return (
    <div className={`sticky top-0 w-full bg-white/95 backdrop-blur-md z-50 transition-all duration-300 ${isScrolled ? 'shadow-lg border-b border-gray-200/50' : 'shadow-sm'}`}>
      {/* Main Header */}
      <header className="px-4 py-3 flex justify-between items-center border-b border-gray-100/60">
        {/* Left side - Search button */}
        <div className="flex items-center">
          {showSearchButton && onSearchClick && (
            <button 
              className={`relative p-2.5 rounded-xl transition-all duration-200 ${
                hasActiveFilters 
                  ? 'text-brand-primary bg-brand-primary/15 hover:bg-brand-primary/25 shadow-md' 
                  : 'text-gray-700 hover:bg-gray-100/80 hover:shadow-sm'
              }`}
              onClick={onSearchClick}
              aria-label="Apri ricerca"
            >
              <Filter className="h-5 w-5 z-40" />
              {hasActiveFilters && activeFiltersCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-brand-primary text-white text-xs rounded-full w-5 h-5 flex items-center justify-center z-50 font-semibold shadow-lg animate-pulse">
                  {activeFiltersCount > 9 ? '9+' : activeFiltersCount}
                </span>
              )}
            </button>
          )}
        </div>

        {/* Center - Title/Greeting */}
        <div className="text-center flex-1">
          {isAuthenticated ? (
            <div className="text-lg font-semibold text-gray-800">
              {getTimeBasedGreeting()} {firstName}
            </div>
          ) : (
            <div className="text-2xl font-bold bg-gradient-to-r from-brand-secondary via-brand-primary to-purple-600 bg-clip-text text-transparent">
              CatchUp
            </div>
          )}
          <div className="text-xs text-gray-500 font-medium mt-0.5">{formatDateInItalian()}</div>
        </div>
        
        {/* Right side - Notifications */}
        <div className="flex items-center">
          {isAuthenticated && (
            <button 
              className="text-gray-700 relative p-2.5 hover:bg-gray-100/80 rounded-xl transition-all duration-200 hover:shadow-sm"
              onClick={handleNotificationClick}
              aria-label="Apri notifiche"
            >
              <MessageCircle className="h-5 w-5" />
              {totalCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-brand-primary text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-semibold shadow-lg animate-pulse">
                  {totalCount > 9 ? '9+' : totalCount}
                </span>
              )}
            </button>
          )}
        </div>
      </header>

      {/* Enhanced Tab Bar with Icons */}
      {tabs.length > 0 && (
        <div 
          className={`px-4 overflow-x-auto transition-all duration-500 ease-in-out ${
            hideTabBar 
              ? '-translate-y-full opacity-0 max-h-0 py-0' 
              : 'translate-y-0 opacity-100 max-h-20 py-3'
          }`}
        >
          <div className="flex space-x-2 min-w-max">
            {tabs.map((tab, index) => {
              const isActive = activeTab === tab.id;
              const IconComponent = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => onTabChange?.(tab.id)}
                  className={`relative px-4 py-2.5 rounded-xl font-medium text-sm transition-all duration-300 whitespace-nowrap flex items-center gap-2 ${
                    isActive
                      ? 'bg-gradient-to-r from-brand-primary to-brand-secondary text-white shadow-lg transform scale-105'
                      : 'bg-gray-100/70 text-gray-600 hover:bg-gray-200/80 hover:text-gray-800 hover:shadow-md hover:scale-102'
                  }`}
                  style={{
                    animationDelay: `${index * 50}ms`
                  }}
                >
                  <IconComponent className={`h-4 w-4 ${isActive ? 'text-white' : 'text-gray-500'} transition-colors duration-300`} />
                  <span className="relative z-10">{tab.label}</span>
                  
                  {/* Active tab indicator */}
                  {isActive && (
                    <div className="absolute inset-0 bg-gradient-to-r from-brand-primary/20 to-brand-secondary/20 rounded-xl animate-pulse" />
                  )}
                  
                  {/* Badge */}
                  {tab.badge && tab.badge > 0 && (
                    <span className={`absolute -top-1 -right-1 text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold shadow-lg z-20 ${
                      isActive 
                        ? 'bg-white text-brand-primary animate-bounce' 
                        : 'bg-brand-primary text-white animate-pulse'
                    }`}>
                      {tab.badge > 9 ? '9+' : tab.badge}
                    </span>
                  )}
                </button>
              );
            })}
          </div>
          
          {/* Decorative gradient line */}
          <div className="mt-2 h-0.5 bg-gradient-to-r from-transparent via-brand-primary/30 to-transparent rounded-full" />
        </div>
      )}
    </div>
  );
};

export default HeaderWithTabs;
