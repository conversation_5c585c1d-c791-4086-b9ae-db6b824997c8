export type AgentType = 'router' | 'booking' | 'discovery' | 'support' | 'business_intelligence' | 'context_manager';

export interface AgentConfiguration {
  id: string;
  agent_type: AgentType;
  name: string;
  description?: string;
  system_prompt: string;
  tools: string[];
  config: Record<string, any>;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface AgentConversation {
  id: string;
  user_id?: string;
  title?: string;
  current_agent_type: AgentType;
  state: Record<string, any>;
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface AgentMessage {
  id: string;
  conversation_id: string;
  agent_type?: AgentType;
  role: 'user' | 'assistant' | 'system';
  content: string;
  metadata: Record<string, any>;
  tool_calls: any[];
  tool_results: any[];
  created_at: string;
}

export interface AgentMetric {
  id: string;
  conversation_id: string;
  agent_type: AgentType;
  metric_type: string;
  value?: number;
  metadata: Record<string, any>;
  created_at: string;
}

export interface AgentResponse {
  agent_type: AgentType;
  content: string;
  tool_calls?: any[];
  tool_results?: any[];
  metadata?: Record<string, any>;
  next_agent?: AgentType;
  state_updates?: Record<string, any>;
}