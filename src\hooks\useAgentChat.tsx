import { useState, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { AgentConversation, AgentMessage, AgentType } from '@/types/agents';
import { toast } from 'sonner';

export const useAgentChat = () => {
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);
  const queryClient = useQueryClient();

  // Fetch current conversation
  const { data: conversation, isLoading: loadingConversation } = useQuery({
    queryKey: ['agent-conversation', currentConversationId],
    queryFn: async (): Promise<AgentConversation | null> => {
      if (!currentConversationId) return null;

      const { data, error } = await supabase
        .from('agent_conversations')
        .select('*')
        .eq('id', currentConversationId)
        .single();

      if (error) {
        console.error('Error fetching conversation:', error);
        throw error;
      }

      return {
        ...data,
        state: typeof data.state === 'object' ? data.state as Record<string, any> : {},
        metadata: typeof data.metadata === 'object' ? data.metadata as Record<string, any> : {}
      };
    },
    enabled: !!currentConversationId,
  });

  // Fetch messages for current conversation
  const { data: messages = [], isLoading: isLoadingMessages } = useQuery({
    queryKey: ['agent-messages', currentConversationId],
    queryFn: async (): Promise<AgentMessage[]> => {
      if (!currentConversationId) return [];

      const { data, error } = await supabase
        .from('agent_messages')
        .select('*')
        .eq('conversation_id', currentConversationId)
        .order('created_at', { ascending: true });

      if (error) {
        console.error('Error fetching messages:', error);
        throw error;
      }

      return data.map(msg => ({
        ...msg,
        role: msg.role as 'user' | 'assistant' | 'system',
        metadata: typeof msg.metadata === 'object' ? msg.metadata as Record<string, any> : {},
        tool_calls: Array.isArray(msg.tool_calls) ? msg.tool_calls : [],
        tool_results: Array.isArray(msg.tool_results) ? msg.tool_results : []
      }));
    },
    enabled: !!currentConversationId,
  });

  // Create new conversation
  const createConversationMutation = useMutation({
    mutationFn: async (title: string): Promise<AgentConversation> => {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .from('agent_conversations')
        .insert({
          title,
          user_id: user.id,
          current_agent_type: 'router' as AgentType,
          state: {},
          metadata: {}
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating conversation:', error);
        throw error;
      }

      return {
        ...data,
        state: typeof data.state === 'object' ? data.state as Record<string, any> : {},
        metadata: typeof data.metadata === 'object' ? data.metadata as Record<string, any> : {}
      };
    },
    onSuccess: (data) => {
      setCurrentConversationId(data.id);
      queryClient.invalidateQueries({ queryKey: ['agent-conversations'] });
    },
  });

  // Send message and get agent response
  const sendMessageMutation = useMutation({
    mutationFn: async ({ 
      content, 
      preferredAgent 
    }: { 
      content: string; 
      preferredAgent?: AgentType;
    }): Promise<void> => {
      let conversationId = currentConversationId;
      
      // Auto-create conversation if none exists
      if (!conversationId) {
        const { data: { user } } = await supabase.auth.getUser();
        
        if (!user) {
          throw new Error('User not authenticated');
        }

        const { data: newConversation, error: createError } = await supabase
          .from('agent_conversations')
          .insert({
            title: 'Nuova Conversazione',
            user_id: user.id,
            current_agent_type: 'router' as AgentType,
            state: {},
            metadata: {}
          })
          .select()
          .single();

        if (createError) {
          console.error('Error creating conversation:', createError);
          throw createError;
        }

        conversationId = newConversation.id;
        setCurrentConversationId(conversationId);
      }

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Insert user message
      const { error: messageError } = await supabase
        .from('agent_messages')
        .insert({
          conversation_id: conversationId,
          role: 'user',
          content,
          metadata: {}
        });

      if (messageError) {
        console.error('Error inserting user message:', messageError);
        throw messageError;
      }

      // Call the agent edge function
      console.log('Calling langgraph-agent with:', {
        conversation_id: conversationId,
        message: content,
        preferred_agent: preferredAgent || 'router'
      });

      const { data: agentResponse, error: agentError } = await supabase.functions.invoke(
        'langgraph-agent',
        {
          body: {
            conversation_id: conversationId,
            message: content,
            preferred_agent: preferredAgent || 'router'
          }
        }
      );

      if (agentError) {
        console.error('Error calling agent:', agentError);
        throw agentError;
      }

      console.log('Agent response:', agentResponse);
    },
    onSuccess: () => {
      // Refresh messages
      queryClient.invalidateQueries({ 
        queryKey: ['agent-messages', currentConversationId] 
      });
      queryClient.invalidateQueries({ 
        queryKey: ['agent-conversation', currentConversationId] 
      });
    },
    onError: (error) => {
      console.error('Error sending message:', error);
      toast.error('Errore nell\'invio del messaggio');
    }
  });

  const createConversation = useCallback(
    (title: string) => createConversationMutation.mutateAsync(title),
    [createConversationMutation]
  );

  const sendMessage = useCallback(
    (content: string, preferredAgent?: AgentType) => 
      sendMessageMutation.mutateAsync({ content, preferredAgent }),
    [sendMessageMutation]
  );

  return {
    conversation,
    messages,
    currentConversationId,
    createConversation,
    sendMessage,
    isLoadingMessages,
    isLoadingConversation: loadingConversation,
    isSendingMessage: sendMessageMutation.isPending,
    isCreatingConversation: createConversationMutation.isPending,
  };
};