
import { Share2, <PERSON>, <PERSON>Lef<PERSON> } from "lucide-react";
import { useNavigate } from "react-router-dom";

interface DealDetailsHeaderProps {
  isFavorite: boolean;
  onToggleFavorite: () => void;
  onShare: () => void;
}

const DealDetailsHeader = ({ isFavorite, onToggleFavorite, onShare }: DealDetailsHeaderProps) => {
  const navigate = useNavigate();

  return (
    <header className="fixed top-0 left-0 right-0 bg-white z-50 px-4 py-3 flex justify-between items-center border-b">
      <button onClick={() => navigate(-1)} className="p-1">
        <ArrowLeft className="h-5 w-5 text-gray-700" />
      </button>
      <div className="flex items-center gap-2">
        <button onClick={onToggleFavorite} className="p-2">
          <Heart className={`h-5 w-5 ${isFavorite ? 'text-red-500 fill-current' : 'text-gray-700'}`} />
        </button>
        <button onClick={onShare} className="p-2">
          <Share2 className="h-5 w-5 text-gray-700" />
        </button>
      </div>
    </header>
  );
};

export default DealDetailsHeader;
