import { <PERSON>, CalendarClock, <PERSON>, <PERSON>, <PERSON>, Ticket, Users, CreditCard, Gift, Share2, FileText, Shield, HelpCircle, MessageCircle, Bot, Database, Settings, Palette, UserPlus, MapPin } from "lucide-react";

const getMenuItems = (
  setIsTermsOpen: (open: boolean) => void,
  setIsPrivacyOpen: (open: boolean) => void
) => [
  {
    icon: Settings,
    label: "Preferenze Personali",
    path: "/personalize-preferences",
    authenticationRequired: true,
    isAdmin: false,
  },
  // {
  //   icon: Heart,
  //   label: "I miei preferiti",
  //   count: "12",
  //   path: "/i-miei-preferiti",
  //   authenticationRequired: true,
  // },
  // {
  //   icon: CalendarClock,
  //   label: "Le mie prenotazioni",
  //   count: "3",
  //   path: "/le-mie-prenotazioni",
  //   authenticationRequired: true,
  // },
  // {
  //   icon: Star,
  //   label: "Le mie recensioni",
  //   count: "8",
  //   path: "/le-mie-recensioni",
  //   authenticationRequired: true,
  // },
  {
    icon: UserPlus,
    label: "I miei gruppi",
    path: "/groups",
    authenticationRequired: true,
    isAdmin: false,
  },
  // {
  //   icon: Bell,
  //   label: "Notifiche",
  //   count: "5",
  //   path: "/notifiche",
  //   authenticationRequired: true,
  // },
  // {
  //   icon: Award,
  //   label: "Punti Fedeltà",
  //   path: "/punti-fedelta",
  //   authenticationRequired: true,
  // },
  // {
  //   icon: Ticket,
  //   label: "Codici Promo",
  //   path: "/codici-promo",
  //   authenticationRequired: true,
  // },
  // {
  //   icon: Users,
  //   label: "Invita un Amico",
  //   path: "/invita-amico",
  //   authenticationRequired: true,
  // },
  // {
  //   icon: CreditCard,
  //   label: "Pagamenti",
  //   path: "/pagamenti",
  //   authenticationRequired: true,
  // },
  // {
  //   icon: Gift,
  //   label: "Carte Regalo",
  //   path: "/carte-regalo",
  //   authenticationRequired: true,
  // },
  // {
  //   icon: Share2,
  //   label: "Condividi l'App",
  //   path: "/condividi",
  //   authenticationRequired: false,
  // },
  {
    icon: Bot,
    label: "Scegli l'assistente",
    path: "/assistant-select",
    authenticationRequired: true,isAdmin: false,
  },
  {
    icon: FileText,
    label: "Termini e Condizioni",
    action: () => setIsTermsOpen(true),
    authenticationRequired: false,
    isAdmin: false,
  },
  {
    icon: Shield,
    label: "Privacy Policy",
    action: () => setIsPrivacyOpen(true),
    authenticationRequired: false,
    isAdmin: false,
  },
  // {
  //   icon: HelpCircle,
  //   label: "Centro Assistenza Clienti",
  //   path: "/assistenza",
  //   authenticationRequired: false,
  // },
  // {
  //   icon: MessageCircle,
  //   label: "Chat Business",
  //   path: "/conversations",
  //   authenticationRequired: false,
  // },
  // {
  //   icon: Bot,
  //   label: "Chat CatchUp",
  //   path: "/chat-catchup",
  //   authenticationRequired: true,
  // },

  {
    icon: MapPin,
    label: "Tracking Dati Posizione",
    path: "/admin/location-tracking",
    authenticationRequired: true,
    isAdmin: true,
  },
  {
    icon: Database,
    label: "Test Data Generator",
    path: "/admin-data",
    authenticationRequired: true,
    isAdmin: true,
  },
  {
    icon: Palette,
    label: "A/B Testing",
    path: "/admin/ab-testing",
    authenticationRequired: true,
    isAdmin: true,
  },
  {
    icon: Bell,
    label: "Push Notification Test",
    path: "/push-notification-test",
    authenticationRequired: true,
    isAdmin: true,
  },
  {
    icon: Bot,
    label: "Admin Agent Chat",
    path: "/admin/agent-chat",
    authenticationRequired: true,
    isAdmin: true,
  },
  {
    id: "subscription",
    icon: CreditCard,
    label: "Piano Sottoscrizione",
    path: "/subscription",
    authenticationRequired: true,
    isAdmin: false,
  }

];

export default getMenuItems;
