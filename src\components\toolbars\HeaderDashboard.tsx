import { MessageCircle, Filter } from "lucide-react";
import { format } from "date-fns";
import { it } from "date-fns/locale";
import { useAuth } from "@/hooks/auth/useAuth";
import { useNotificationCount } from "@/hooks/useNotificationCount";
import { useNavigate } from "react-router-dom";

interface HeaderDashboardProps {
  onSearchClick?: () => void;
  showSearchButton?: boolean;
  activeFiltersCount?: number;
  hasActiveFilters?: boolean;
}

const HeaderDashboard = ({ 
  onSearchClick, 
  showSearchButton = false, 
  activeFiltersCount = 0,
  hasActiveFilters = false 
}: HeaderDashboardProps) => {
  const { userDetails, isAuthenticated } = useAuth();
  const { totalCount } = useNotificationCount();
  const navigate = useNavigate();

  const handleNotificationClick = () => {
    navigate('/conversations?tab=notifications');
  };

  const formatDateInItalian = () => {
    const formatted = format(new Date(), "EEEE, d MMMM", { locale: it });

    // Split by delimiters, capitalize each part that needs it, and rejoin
    const parts = formatted.split(", ");
    const weekday = parts[0].charAt(0).toUpperCase() + parts[0].slice(1);

    const dayAndMonth = parts[1].split(" ");
    const day = dayAndMonth[0];
    const month =
      dayAndMonth[1].charAt(0).toUpperCase() + dayAndMonth[1].slice(1);

    return `${weekday}, ${day} ${month}`;
  };

  /**
   * Returns a greeting based on the current time of day in Italian
   * @returns {string} - A greeting message appropriate for the current time
   */
  const getTimeBasedGreeting = () => {
    const hour = new Date().getHours();

    if (hour >= 5 && hour < 12) {
      return "Buongiorno";
    } else if (hour >= 12 && hour < 18) {
      return "Buon pomeriggio";
    } else if (hour >= 18 && hour < 22) {
      return "Buona sera";
    } else {
      return "Buona notte";
    }
  };

  // Get user's first name or default to "Utente" if not available
  const firstName = userDetails?.first_name || "Utente";

  return (
    <header className="fixed top-0 w-full bg-white shadow-sm z-50 px-4 py-3 flex justify-between items-center">
      {/* Left side - Search button (when enabled) */}
      <div className="flex items-center">
        {showSearchButton && onSearchClick && (
          <button 
            className={`relative p-2 rounded-full transition-colors ${
              hasActiveFilters 
                ? 'text-brand-primary bg-brand-primary/10 hover:bg-brand-primary/20' 
                : 'text-gray-700 hover:bg-gray-100'
            }`}
            onClick={onSearchClick}
            aria-label="Apri ricerca"
          >
            <Filter className="h-5 w-5" />
            {hasActiveFilters && activeFiltersCount > 0 && (
              <span className="absolute -top-1 -right-1 bg-brand-primary text-white text-xs rounded-full w-4 h-4 flex items-center justify-center font-medium">
                {activeFiltersCount > 9 ? '9+' : activeFiltersCount}
              </span>
            )}
          </button>
        )}
      </div>

      {/* Center - Title/Greeting */}
      <div className="text-center flex-1">
        {isAuthenticated ? (
          <div className="text-lg font-medium">
            {getTimeBasedGreeting()} {firstName}
          </div>
        ) : (
          <div className="text-2xl font-bold bg-gradient-to-r from-brand-secondary to-brand-primary bg-clip-text text-transparent">
            CatchUp
          </div>
        )}
        <div className="text-xs mt-1">{formatDateInItalian()}</div>
      </div>
      
      {/* Right side - Notifications */}
      <div className="flex items-center">
        {isAuthenticated && (
          <button 
            className="text-gray-700 relative p-2 hover:bg-gray-100 rounded-full transition-colors"
            onClick={handleNotificationClick}
            aria-label="Apri notifiche"
          >
            <MessageCircle className="h-5 w-5" />
            {totalCount > 0 && (
              <span className="absolute -top-1 -right-1 bg-brand-primary text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                {totalCount}
              </span>
            )}
          </button>
        )}
      </div>
    </header>
  );
};

export default HeaderDashboard;
