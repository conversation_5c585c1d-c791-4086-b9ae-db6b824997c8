import React, { useState, useEffect, useRef } from 'react';
import { Bo<PERSON>, Send, Settings, User, Zap, Database, Compass, HeadphonesIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAgentChat } from '@/hooks/useAgentChat';
import { useAgentConfigurations } from '@/hooks/useAgentConfigurations';
import { useAuth } from '@/hooks/auth/useAuth';
import { toast } from 'sonner';

interface AgentMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  agent_type?: string;
  metadata?: Record<string, any>;
  tool_calls?: any[];
  tool_results?: any[];
  created_at: string;
}

const AgentIcons = {
  router: Bot,
  booking: Zap,
  discovery: Compass,
  support: HeadphonesIcon,
  business_intelligence: Database,
  context_manager: Settings,
};

const AdminAgentChat: React.FC = () => {
  const [currentMessage, setCurrentMessage] = useState('');
  const [selectedAgent, setSelectedAgent] = useState<string>('router');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  const { user, isLoading: authLoading } = useAuth();
  
  const {
    conversation,
    messages,
    createConversation,
    sendMessage,
    isLoadingMessages
  } = useAgentChat();
  
  const { configurations, isLoading: loadingConfigs } = useAgentConfigurations();

  // Define functions before hooks that use them
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // All hooks must be called before any conditional returns
  useEffect(() => {
    // Create conversation on component mount
    if (user) {
      createConversation('Admin Agent Testing Session');
    }
  }, [createConversation, user]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Show loading if auth is still loading
  if (authLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <p className="text-muted-foreground">Caricamento...</p>
        </div>
      </div>
    );
  }

  // Show login prompt if not authenticated
  if (!user) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Accesso Richiesto</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              Devi essere autenticato per accedere al centro di controllo agenti.
            </p>
            <Button onClick={() => window.location.href = '/auth/login'} className="w-full">
              Vai al Login
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const handleSendMessage = async () => {
    if (!currentMessage.trim()) return;

    setIsLoading(true);
    try {
      await sendMessage(currentMessage, selectedAgent as any);
      setCurrentMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Errore nell\'invio del messaggio');
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const getAgentIcon = (agentType: string) => {
    const IconComponent = AgentIcons[agentType as keyof typeof AgentIcons] || Bot;
    return <IconComponent className="h-4 w-4" />;
  };

  const getAgentConfig = (agentType: string) => {
    return configurations?.find(config => config.agent_type === agentType);
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto max-w-6xl p-4">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-foreground mb-2">
            Centro di Controllo Agenti AI
          </h1>
          <p className="text-muted-foreground">
            Test e gestione del sistema multi-agente CatchUp
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Agent Selection & Configuration */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Configurazione
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Agente Attivo
                  </label>
                  <Select value={selectedAgent} onValueChange={setSelectedAgent}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {configurations?.map((config) => (
                        <SelectItem key={config.agent_type} value={config.agent_type}>
                          <div className="flex items-center gap-2">
                            {getAgentIcon(config.agent_type)}
                            {config.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Agent Info */}
                {getAgentConfig(selectedAgent) && (
                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">Informazioni Agente</h4>
                      <div className="text-xs text-muted-foreground">
                      <p><strong>Tipo:</strong> {getAgentConfig(selectedAgent)?.agent_type}</p>
                      <p><strong>Descrizione:</strong> {getAgentConfig(selectedAgent)?.description}</p>
                      <p><strong>Strumenti:</strong> {getAgentConfig(selectedAgent)?.tools?.length || 0}</p>
                    </div>
                  </div>
                )}

                {/* Conversation Info */}
                {conversation && (
                  <div className="space-y-2 pt-4 border-t">
                    <h4 className="font-medium text-sm">Conversazione</h4>
                    <div className="text-xs text-muted-foreground">
                      <p><strong>ID:</strong> {conversation.id.slice(0, 8)}...</p>
                      <p><strong>Messaggi:</strong> {messages.length}</p>
                      <p><strong>Agente Corrente:</strong> {conversation.current_agent_type}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Chat Interface */}
          <div className="lg:col-span-3">
            <Card className="h-[600px] flex flex-col">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bot className="h-5 w-5" />
                  Chat Multi-Agente
                  {conversation && (
                    <Badge variant="secondary" className="ml-auto">
                      {conversation.current_agent_type}
                    </Badge>
                  )}
                </CardTitle>
              </CardHeader>
              
              <CardContent className="flex-1 flex flex-col p-0">
                {/* Messages Area */}
                <div className="flex-1 overflow-y-auto p-4 space-y-4">
                  {isLoadingMessages ? (
                    <div className="text-center text-muted-foreground">
                      Caricamento messaggi...
                    </div>
                  ) : messages.length === 0 ? (
                    <div className="text-center text-muted-foreground">
                      Inizia una conversazione con gli agenti AI
                    </div>
                  ) : (
                    messages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex gap-3 ${
                          message.role === 'user' ? 'justify-end' : 'justify-start'
                        }`}
                      >
                        {message.role !== 'user' && (
                          <div className="flex-shrink-0">
                            <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                              {getAgentIcon(message.agent_type || 'router')}
                            </div>
                          </div>
                        )}
                        
                        <div
                          className={`max-w-[80%] rounded-lg p-3 ${
                            message.role === 'user'
                              ? 'bg-primary text-primary-foreground'
                              : 'bg-muted'
                          }`}
                        >
                          <div className="text-sm font-medium mb-1">
                            {message.role === 'user' ? (
                              <User className="h-4 w-4 inline mr-1" />
                            ) : (
                              <>
                                {getAgentIcon(message.agent_type || 'router')}
                                <span className="ml-1 capitalize">
                                  {message.agent_type || 'System'}
                                </span>
                              </>
                            )}
                          </div>
                          <div className="whitespace-pre-wrap">{message.content}</div>
                          
                          {/* Tool calls and results */}
                          {message.tool_calls && message.tool_calls.length > 0 && (
                            <div className="mt-2 text-xs opacity-75">
                              <details>
                                <summary>Chiamate Strumenti ({message.tool_calls.length})</summary>
                                <pre className="mt-1 overflow-x-auto">
                                  {JSON.stringify(message.tool_calls, null, 2)}
                                </pre>
                              </details>
                            </div>
                          )}
                          
                          <div className="text-xs opacity-50 mt-1">
                            {new Date(message.created_at).toLocaleTimeString()}
                          </div>
                        </div>
                        
                        {message.role === 'user' && (
                          <div className="flex-shrink-0">
                            <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center">
                              <User className="h-4 w-4 text-primary-foreground" />
                            </div>
                          </div>
                        )}
                      </div>
                    ))
                  )}
                  <div ref={messagesEndRef} />
                </div>

                {/* Input Area */}
                <div className="border-t p-4">
                  <div className="flex gap-2">
                    <Input
                      value={currentMessage}
                      onChange={(e) => setCurrentMessage(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder={
                        !conversation 
                          ? "Creazione conversazione..." 
                          : "Scrivi un messaggio agli agenti..."
                      }
                      disabled={isLoading}
                      className="flex-1"
                    />
                    <Button
                      onClick={handleSendMessage}
                      disabled={isLoading || !currentMessage.trim()}
                      size="icon"
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <div className="flex items-center gap-2 mt-2 text-xs text-muted-foreground">
                    <span>Agente selezionato:</span>
                    <Badge variant="outline" className="flex items-center gap-1">
                      {getAgentIcon(selectedAgent)}
                      {getAgentConfig(selectedAgent)?.name || selectedAgent}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminAgentChat;