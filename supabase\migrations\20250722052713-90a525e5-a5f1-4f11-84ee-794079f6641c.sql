
-- Create enum for agent types
CREATE TYPE agent_type_enum AS ENUM ('router', 'booking', 'discovery', 'support', 'business_intelligence', 'context_manager');

-- Create table for agent configurations
CREATE TABLE public.agent_configurations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_type agent_type_enum NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    system_prompt TEXT NOT NULL,
    tools JSONB DEFAULT '[]'::jsonb,
    config JSONB DEFAULT '{}'::jsonb,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create table for agent conversations
CREATE TABLE public.agent_conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT,
    current_agent_type agent_type_enum DEFAULT 'router',
    state J<PERSON>NB DEFAULT '{}'::jsonb,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create table for agent messages
CREATE TABLE public.agent_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID REFERENCES agent_conversations(id) ON DELETE CASCADE,
    agent_type agent_type_enum,
    role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}'::jsonb,
    tool_calls JSONB DEFAULT '[]'::jsonb,
    tool_results JSONB DEFAULT '[]'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create table for agent performance metrics
CREATE TABLE public.agent_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID REFERENCES agent_conversations(id) ON DELETE CASCADE,
    agent_type agent_type_enum NOT NULL,
    metric_type TEXT NOT NULL,
    value NUMERIC,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Enable RLS
ALTER TABLE public.agent_configurations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agent_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agent_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agent_metrics ENABLE ROW LEVEL SECURITY;

-- RLS Policies for agent_configurations
CREATE POLICY "Admins can manage agent configurations" ON public.agent_configurations
    FOR ALL USING (get_user_role(auth.uid()) = 'admin');

CREATE POLICY "Everyone can view active agent configurations" ON public.agent_configurations
    FOR SELECT USING (is_active = true);

-- RLS Policies for agent_conversations
CREATE POLICY "Admins can manage all agent conversations" ON public.agent_conversations
    FOR ALL USING (get_user_role(auth.uid()) = 'admin');

CREATE POLICY "Users can manage their own agent conversations" ON public.agent_conversations
    FOR ALL USING (auth.uid() = user_id);

-- RLS Policies for agent_messages
CREATE POLICY "Admins can manage all agent messages" ON public.agent_messages
    FOR ALL USING (get_user_role(auth.uid()) = 'admin');

CREATE POLICY "Users can manage messages in their conversations" ON public.agent_messages
    FOR ALL USING (
        conversation_id IN (
            SELECT id FROM agent_conversations WHERE user_id = auth.uid()
        )
    );

-- RLS Policies for agent_metrics
CREATE POLICY "Admins can view all agent metrics" ON public.agent_metrics
    FOR SELECT USING (get_user_role(auth.uid()) = 'admin');

-- Insert default agent configurations
INSERT INTO public.agent_configurations (agent_type, name, description, system_prompt, tools, config) VALUES
('router', 'Router Agent', 'Analyzes user intent and routes to specialized agents', 
 'You are a router agent that analyzes user messages and determines which specialized agent should handle the request. Available agents: booking, discovery, support, business_intelligence. Always respond with the agent type and brief reasoning.',
 '["route_to_agent"]'::jsonb, '{"max_retries": 3}'::jsonb),

('booking', 'Booking Agent', 'Handles all booking-related operations and inquiries',
 'You are a booking agent specialized in handling reservations, cancellations, and booking-related inquiries for CatchUp. You can search deals, check availability, create bookings, and manage existing reservations.',
 '["search_deals", "check_availability", "create_booking", "cancel_booking", "get_booking_details"]'::jsonb, '{"auto_confirm": true}'::jsonb),

('discovery', 'Discovery Agent', 'Manages deal search and personalized recommendations',
 'You are a discovery agent that helps users find relevant deals and businesses based on their preferences, location, and behavior. You provide personalized recommendations and search results.',
 '["search_deals", "get_recommendations", "search_businesses", "get_user_preferences"]'::jsonb, '{"recommendation_limit": 10}'::jsonb),

('support', 'Support Agent', 'Handles general inquiries and customer support',
 'You are a customer support agent for CatchUp. You help users with general questions, account issues, and provide assistance with using the platform.',
 '["get_user_profile", "send_notification", "escalate_issue"]'::jsonb, '{"escalation_threshold": 3}'::jsonb),

('business_intelligence', 'BI Agent', 'Provides analytics and business insights',
 'You are a business intelligence agent that provides data-driven insights, analytics, and reports about CatchUp platform performance, user behavior, and business metrics.',
 '["get_analytics", "generate_report", "query_database", "get_business_metrics"]'::jsonb, '{"report_formats": ["json", "csv"]}'::jsonb),

('context_manager', 'Context Manager', 'Maintains conversation state and user context',
 'You are a context manager that maintains conversation state, user preferences, and context across agent interactions. You ensure continuity and personalization.',
 '["save_context", "load_context", "update_preferences"]'::jsonb, '{"context_retention": "7d"}'::jsonb);

-- Create indexes for performance
CREATE INDEX idx_agent_conversations_user_id ON public.agent_conversations(user_id);
CREATE INDEX idx_agent_messages_conversation_id ON public.agent_messages(conversation_id);
CREATE INDEX idx_agent_messages_agent_type ON public.agent_messages(agent_type);
CREATE INDEX idx_agent_metrics_conversation_id ON public.agent_metrics(conversation_id);
CREATE INDEX idx_agent_metrics_agent_type ON public.agent_metrics(agent_type);

-- Create trigger for updating timestamps
CREATE OR REPLACE FUNCTION update_agent_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_agent_conversations_updated_at
    BEFORE UPDATE ON public.agent_conversations
    FOR EACH ROW EXECUTE FUNCTION update_agent_updated_at();

CREATE TRIGGER update_agent_configurations_updated_at
    BEFORE UPDATE ON public.agent_configurations
    FOR EACH ROW EXECUTE FUNCTION update_agent_updated_at();
