import { useNavigate, useSearchParams } from "react-router-dom";
import { useConversations } from "@/hooks/useConversations";
import { useNotifications } from "@/hooks/notifications/useNotifications";
import { useNotificationDeduplication } from "@/hooks/notifications/useNotificationDeduplication";
import { format } from "date-fns";
import { it } from "date-fns/locale";
import Header from "@/components/toolbars/Header";
import BottomNavigationBar from "@/components/toolbars/BottomNavigationBar";
import { useBusinessMode } from "@/hooks/useBusinessMode";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";
import { EmptyConversationsState, EmptyNotificationsState } from "@/components/ui/empty-states";
import SwipeableGroupInviteCard from "@/components/notifications/SwipeableGroupInviteCard";
import EnhancedNotificationCard from "@/components/notifications/EnhancedNotificationCard";
import SwipeHintOverlay from "@/components/notifications/SwipeHintOverlay";

const Conversations = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { conversations, isLoading } = useConversations();
  const { data: notifications = [], isLoading: isLoadingNotifications } = useNotifications();
  const { isBusinessMode } = useBusinessMode();
  
  // Determina il tab attivo dai parametri URL o default a "conversations"
  const tabFromUrl = searchParams.get('tab');
  const [activeTab, setActiveTab] = useState<"conversations" | "notifications">(
    tabFromUrl === 'notifications' ? 'notifications' : 'conversations'
  );

  // Gestione del tutorial swipe
  const [showSwipeHint, setShowSwipeHint] = useState(false);

  // Separiamo le conversazioni normali da quelle di booking
  const regularConversations = conversations.filter(conv => !conv.booking_id);
  const bookingConversations = conversations.filter(conv => conv.booking_id);

  // Usa il hook di deduplicazione
  const { 
    groupInviteNotifications, 
    bookingNotifications, 
    otherNotifications 
  } = useNotificationDeduplication(notifications, bookingConversations);

  // Mostra il tutorial al primo accesso se ci sono inviti di gruppo
  useEffect(() => {
    const hasSeenSwipeHint = localStorage.getItem('hasSeenSwipeHint');
    if (!hasSeenSwipeHint && groupInviteNotifications.length > 0 && activeTab === 'notifications') {
      setShowSwipeHint(true);
    }
  }, [groupInviteNotifications.length, activeTab]);

  const handleDismissSwipeHint = () => {
    setShowSwipeHint(false);
    localStorage.setItem('hasSeenSwipeHint', 'true');
  };

  if (isLoading || isLoadingNotifications) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-pulse">Caricamento...</div>
      </div>
    );
  }

  const handleNotificationClick = (notification: any) => {
    // Gestisci il click sulla notifica basandosi sull'entity
    switch (notification.entity) {
      case 'group_invites':
        navigate('/groups');
        break;
      case 'bookings':
        // Naviga alla conversazione della prenotazione
        navigate(`/conversations/${notification.entity_id}`);
        break;
      default:
        console.log('Notifica cliccata:', notification);
    }
  };

  const handleGroupInviteProcessed = () => {
    // Forza il refresh delle notifiche dopo l'elaborazione
    window.location.reload();
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <div className="fixed top-0 left-0 right-0">
        <Header title="Messaggi" isBusiness={isBusinessMode} />
      </div>

      <main className="flex-1 overflow-y-auto pt-16 pb-20 px-4">
        {/* Tab Buttons */}
        <div className="flex items-center justify-center gap-2 rounded-lg p-3 mb-4 mt-4">
          <Button
            className={`text-sm rounded-full w-full transition-all duration-300 ${
              activeTab === "conversations"
                ? "bg-primary text-white"
                : "bg-white text-gray-600 border-gray-300"
            }`}
            onClick={() => setActiveTab("conversations")}
          >
            <span>Conversazioni</span>
          </Button>
          <Button
            className={`text-sm rounded-full w-full transition-all duration-300 ${
              activeTab === "notifications"
                ? "bg-primary text-white"
                : "bg-white text-gray-600 border-gray-300"
            }`}
            onClick={() => setActiveTab("notifications")}
          >
            <span>Notifiche</span>
            {(groupInviteNotifications.length + bookingConversations.length + bookingNotifications.length + otherNotifications.length) > 0 && (
              <span className="ml-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {groupInviteNotifications.length + bookingConversations.length + bookingNotifications.length + otherNotifications.length}
              </span>
            )}
          </Button>
        </div>

        {/* Sliding Content Container */}
        <div className="relative overflow-hidden">
          <div 
            className={`flex transition-transform duration-500 ease-in-out ${
              activeTab === "conversations" ? "transform translate-x-0" : "transform -translate-x-1/2"
            }`}
            style={{ width: "200%" }}
          >
            {/* Conversations Tab Content - Solo conversazioni normali */}
            <div className="w-1/2 pr-4">
              {regularConversations.length === 0 ? (
                <EmptyConversationsState />
              ) : (
                <div className="space-y-4">
                  {regularConversations.map((conversation) => (
                    <div
                      key={conversation.id}
                      onClick={() => navigate(`/conversations/${conversation.id}`)}
                      className="bg-white rounded-lg p-4 shadow-sm cursor-pointer hover:bg-gray-50 relative"
                    >
                      <div className="flex items-center gap-4">
                        {conversation.business_photo ? (
                          <img
                            src={conversation.business_photo}
                            alt={conversation.business_name}
                            className="h-12 w-12 rounded-full object-cover"
                          />
                        ) : (
                          <div className="h-12 w-12 rounded-full bg-gray-200" />
                        )}
                        <div className="flex-1">
                          <div className="flex justify-between items-start">
                            <div>
                              <h3 className="font-medium">
                                {conversation.business_name}
                              </h3>
                              <div className="flex items-center gap-2">
                                {conversation.deal_title && (
                                  <p className="text-sm text-gray-500">
                                    {conversation.deal_title}
                                  </p>
                                )}
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              {conversation.unread_count > 0 && (
                                <div className="bg-brand-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                                  {conversation.unread_count}
                                </div>
                              )}
                              {conversation.last_message && (
                                <span className="text-xs text-gray-500">
                                  {format(
                                    new Date(conversation.last_message.created_at),
                                    "HH:mm",
                                    {
                                      locale: it,
                                    }
                                  )}
                                </span>
                              )}
                            </div>
                          </div>
                          {conversation.last_message && (
                            <p className="text-sm text-gray-600 mt-1 line-clamp-1">
                              {conversation.last_message.content}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Notifications Tab Content - Nuova implementazione unificata */}
            <div className="w-1/2 pl-4">
              {(groupInviteNotifications.length === 0 && bookingConversations.length === 0 && bookingNotifications.length === 0 && otherNotifications.length === 0) ? (
                <EmptyNotificationsState />
              ) : (
                <div className="space-y-4">
                  {/* Group Invite Cards - Swipeable */}
                  {groupInviteNotifications.map((notification) => (
                    <SwipeableGroupInviteCard
                      key={notification.id}
                      notification={notification}
                      onProcessed={handleGroupInviteProcessed}
                    />
                  ))}

                  {/* Booking Conversations - Stile distintivo */}
                  {bookingConversations.map((conversation) => (
                    <EnhancedNotificationCard
                      key={`booking-conv-${conversation.id}`}
                      notification={{
                        id: conversation.id,
                        entity: 'bookings',
                        entity_id: conversation.id,
                        created_at: conversation.last_message?.created_at || new Date().toISOString()
                      }}
                      onClick={() => navigate(`/conversations/${conversation.id}`)}
                      variant="booking"
                    />
                  ))}

                  {/* Booking Notifications - Solo se non duplicate */}
                  {bookingNotifications.map((notification) => (
                    <EnhancedNotificationCard
                      key={notification.id}
                      notification={notification}
                      onClick={() => handleNotificationClick(notification)}
                      variant="default"
                    />
                  ))}

                  {/* Other Notifications */}
                  {otherNotifications.map((notification) => (
                    <EnhancedNotificationCard
                      key={notification.id}
                      notification={notification}
                      onClick={() => handleNotificationClick(notification)}
                      variant="default"
                    />
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </main>

      <div className="fixed bottom-0 left-0 right-0">
        <BottomNavigationBar isBusiness={isBusinessMode} />
      </div>

      {/* Swipe Tutorial Overlay */}
      <SwipeHintOverlay 
        show={showSwipeHint}
        onDismiss={handleDismissSwipeHint}
      />
    </div>
  );
};

export default Conversations;
