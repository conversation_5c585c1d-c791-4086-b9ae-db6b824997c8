
import { Store, Bell } from "lucide-react";
import { useNotificationCount } from "@/hooks/useNotificationCount";
import { useAuth } from "@/hooks/auth/useAuth";
import { useNavigate } from "react-router-dom";

interface HeaderProps {
  title: string;
  isBusiness: boolean;
}

const Header = ({ title, isBusiness }: HeaderProps) => {
  const { isAuthenticated } = useAuth();
  const { totalCount } = useNotificationCount();
  const navigate = useNavigate();

  const handleNotificationClick = () => {
    navigate('/conversations?tab=notifications');
  };

  return (
    <header className="fixed top-0 w-full bg-white shadow-sm z-50 px-4 py-3 flex justify-between items-center">
      <h1 className="text-xl text-gray-800 text-center w-full font-semibold">
        {title}
      </h1>
      
      <div className="flex items-center gap-2">
        {isBusiness && <Store className="h-6 w-6 text-brand-primary" />}
        {isAuthenticated && (
          <button 
            className="text-gray-700 relative"
            onClick={handleNotificationClick}
          >
            <Bell className="h-6 w-6" />
            {totalCount > 0 && (
              <span className="absolute -top-1 -right-1 bg-brand-primary text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                {totalCount}
              </span>
            )}
          </button>
        )}
      </div>
    </header>
  );
};

export default Header;
